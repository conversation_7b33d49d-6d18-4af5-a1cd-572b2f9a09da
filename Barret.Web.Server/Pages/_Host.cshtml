@page "/"
@using Microsoft.AspNetCore.Components.Web
@namespace Barret.Web.Server.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />

    <!-- jQuery first -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap CSS with lower specificity -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" media="print" onload="this.media='all'" />



    <!-- Modern CSS Custom Properties Design System -->
    <link href="css/themes/light.css" rel="stylesheet" />

    <!-- Custom Material Theme (Free) -->
    <link href="css/themes/radzen-material3-custom.css" rel="stylesheet" />

    <!-- Tailwind CSS with integrated design system -->
    <link href="css/Styles/dist.css" rel="stylesheet" />



    <!-- Icons -->
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <link
        href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp"
        rel="stylesheet">

    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>

<body>
    <component type="typeof(App)" render-mode="ServerPrerendered" />

    <div id="blazor-error-ui" style="display: none;">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        @* <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment> *@
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <script src="js/toastConfirmation.js"></script>
    <script src="js/toastService.js"></script>
    <script src="js/fileDownload.js"></script>
    <script src="js/fileUtils.js"></script>
    <script src="js/navigation-helpers.js"></script>
    <script src="js/vehicle-editor-new.js"></script>
    <script src="js/blazor-error-ui.js"></script>

    <script>
        window.storeInterfaceForEdit = function (device) {
            window.currentInterfaceToEdit = device;
        };

        window.openInterfaceEditModal = function () {
            // Use a setTimeout to ensure this runs after the current modal event processing is complete
            setTimeout(function () {
                DotNet.invokeMethodAsync('Barret.Web.Server', 'OpenInterfaceEditor', window.currentInterfaceToEdit);
            }, 100);
        };
    </script>
</body>

</html>
