@using Microsoft.AspNetCore.Components.Web
@using Radzen.Blazor
@using Barret.Web.Server.Features.Shared.Components
@inherits LayoutComponentBase

<PageTitle>Barret - Vehicle Configurator</PageTitle>

@* Replaced RadzenTheme component with custom Material 3 theme CSS *@

<div class="min-h-screen flex flex-col bg-white">
    <!-- Header with user dropdown and theme toggle -->
    <header class="bg-white border-b border-gray-100">
        <div class="flex justify-between items-center px-4 py-3">
            <div class="flex items-center">
                <ThemeToggle />
            </div>
            <div class="flex items-center">
                <Barret.Web.Server.Shared.LoginDisplay />
            </div>
        </div>
    </header>

    <!-- Content -->
    <main class="flex-1 bg-white">
        @Body
    </main>
</div>

<!-- Radzen Components - includes dialog support -->
<RadzenComponents @rendermode="RenderMode.InteractiveServer" />

<!-- Radzen Dialog Component - Required for DialogService -->
<RadzenDialog @rendermode="RenderMode.InteractiveServer" />

<!-- Radzen Notification Component - For notifications -->
<RadzenNotification @rendermode="RenderMode.InteractiveServer" />
