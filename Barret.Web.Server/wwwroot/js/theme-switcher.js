/**
 * Theme Switcher for Barret Vehicle Configurator
 * Handles switching between light and dark themes
 */

window.updateTheme = function(theme) {
    // Find the theme CSS link element
    const themeLink = document.querySelector('link[href*="css/themes/"]');

    if (themeLink) {
        // Update the href to point to the correct theme file
        const newHref = theme === 'dark'
            ? 'css/themes/dark.css'
            : 'css/themes/light.css';

        // Only update if different to avoid unnecessary reloads
        if (themeLink.href !== newHref) {
            themeLink.href = newHref;
        }

        // Save theme preference to localStorage
        try {
            localStorage.setItem('barret-theme', theme);
        } catch (e) {
            console.warn('Could not save theme to localStorage:', e);
        }

        // Update data-theme attribute on document for CSS targeting
        document.documentElement.setAttribute('data-theme', theme);

        console.log(`Theme switched to: ${theme}`);
    } else {
        console.error('Theme CSS link not found');
    }
};

// Initialize theme on page load
window.initializeTheme = function() {
    // Get saved theme from localStorage or default to light
    const savedTheme = localStorage.getItem('barret-theme') || 'light';
    
    // Set initial data-theme attribute
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    // Update theme link if needed
    window.updateTheme(savedTheme);
    
    return savedTheme;
};

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', function() {
    window.initializeTheme();
});
