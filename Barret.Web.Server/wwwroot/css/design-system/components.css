/* ===== BUTTON SYSTEM ===== */
.btn {
  /* Base button styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  transition: all var(--transition-normal);
  cursor: pointer;
  border: none;
  text-decoration: none;
  font-family: inherit;
  
  /* Prevent text selection */
  user-select: none;
  
  /* Focus styles */
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  height: 2rem;
  min-width: 4rem;
}

.btn-md {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  height: 2.5rem;
  min-width: 5rem;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
  height: 3rem;
  min-width: 6rem;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  border: none;
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);

  &:hover:not(:disabled) {
    background-color: var(--color-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-secondary-contrast);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);

  &:hover:not(:disabled) {
    background-color: var(--color-secondary-hover);
    border-color: var(--color-gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }
}

.btn-success {
  background-color: var(--color-success);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: var(--color-success-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: var(--color-danger-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

/* Disabled state */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ===== CARD SYSTEM ===== */
.card {
  background-color: var(--color-secondary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card-interactive {
  cursor: pointer;
  
  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--color-gray-300);
    transform: translateY(-1px);
  }
}

/* Card Sizes */
.card-sm { padding: var(--spacing-4); }
.card-md { padding: var(--spacing-6); }
.card-lg { padding: var(--spacing-8); }

/* Card Variants */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-outlined {
  border: 2px solid var(--color-gray-200);
  box-shadow: none;
}

/* ===== FORM SYSTEM ===== */
.form-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  background-color: var(--color-secondary);
  color: var(--color-secondary-contrast);
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
  font-family: inherit;
  
  &::placeholder {
    color: var(--color-gray-500);
  }
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgb(17 24 39 / 0.1);
  }
  
  &:disabled {
    background-color: var(--color-gray-100);
    color: var(--color-gray-500);
    cursor: not-allowed;
  }
  
  &.form-input-error {
    border-color: var(--color-danger);
    
    &:focus {
      border-color: var(--color-danger);
      box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
    }
  }
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-error {
  color: var(--color-danger);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

/* ===== BADGE SYSTEM ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-success {
  background-color: rgb(34 197 94 / 0.1);
  color: var(--color-success);
}

.badge-warning {
  background-color: rgb(245 158 11 / 0.1);
  color: var(--color-warning);
}

.badge-danger {
  background-color: rgb(239 68 68 / 0.1);
  color: var(--color-danger);
}

.badge-neutral {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

/* ===== DIALOG SYSTEM ===== */
.dialog {
  /* Base dialog content styling - works with RadzenDialog */
  background-color: var(--color-secondary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  font-family: inherit;
  color: var(--color-secondary-contrast);
}

/* Dialog Header */
.dialog-header {
  background-color: var(--color-secondary);
  border-bottom: none;
  padding: var(--spacing-6);
}

.dialog-title {
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  margin: 0;
  line-height: 1.5;
}

/* Dialog Content */
.dialog-content {
  padding: var(--spacing-6);
  background-color: var(--color-secondary);
  color: var(--color-secondary-contrast);
}

/* Dialog Footer */
.dialog-footer {
  background-color: var(--color-secondary);
  border-top: none;
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  align-items: center;
}

/* Dialog Size Variants */
.dialog-sm {
  max-width: 400px;
}

.dialog-md {
  max-width: 500px;
}

.dialog-lg {
  max-width: 700px;
}

.dialog-xl {
  max-width: 900px;
}

/* Dialog Content Variants */
.dialog-content-centered {
  text-align: center;
}

.dialog-content-form {
  /* Optimized for form layouts */
  padding: var(--spacing-6);
}

/* Dialog Icon Container */
.dialog-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-4);
}

.dialog-icon-success {
  background-color: rgb(34 197 94 / 0.1);
  color: var(--color-success);
}

.dialog-icon-warning {
  background-color: rgb(245 158 11 / 0.1);
  color: var(--color-warning);
}

.dialog-icon-danger {
  background-color: rgb(239 68 68 / 0.1);
  color: var(--color-danger);
}

.dialog-icon-info {
  background-color: rgb(59 130 246 / 0.1);
  color: #3b82f6;
}

/* ===== DIALOG ENHANCEMENTS ===== */
/* Ensure proper text styling in dialogs */
.dialog h1, .dialog h2, .dialog h3, .dialog h4, .dialog h5, .dialog h6 {
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
  line-height: 1.5;
  margin: 0;
}

.dialog p {
  color: var(--color-gray-600);
  line-height: 1.6;
  margin: 0;
}

/* Dialog content spacing improvements */
.dialog-content > * + * {
  margin-top: var(--spacing-4);
}

.dialog-content-form > * + * {
  margin-top: var(--spacing-4);
}

/* Ensure buttons in dialogs have proper styling */
.dialog .btn {
  font-family: inherit;
  white-space: nowrap;
}

/* Dialog responsive improvements */
@media (max-width: 640px) {
  .dialog-footer {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .dialog-footer .btn {
    width: 100%;
  }
}
