/* ===== RADZEN BUTTON OVERRIDES ===== */
/* Integration with Radzen Material theme + our design tokens */

.rz-button {
  /* Apply universal button styling by default */
  font-family: inherit !important;

  /* Override Radzen Material theme with our design tokens */
  --rz-primary: var(--color-primary) !important;
  --rz-primary-lighter: var(--color-primary-hover) !important;
  --rz-primary-darker: var(--color-primary-hover) !important;
  --rz-secondary: var(--color-secondary) !important;
  --rz-secondary-lighter: var(--color-secondary-hover) !important;

  /* Button styling integration */
  border-radius: var(--radius-full) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--transition-normal) !important;

  /* When using our universal classes, they take precedence */
  &.barret-btn-primary {
    /* All styling comes from .barret-btn-primary in components.css */
  }

  &.barret-btn-secondary {
    /* All styling comes from .barret-btn-secondary in components.css */
  }
}

/* ===== RADZEN DIALOG OVERRIDES ===== */
.rz-dialog {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--color-gray-200) !important;
  background: var(--color-secondary) !important;
  max-width: 95vw !important;
  max-height: 95vh !important;
  overflow: hidden !important;
  font-family: inherit !important;

  .rz-dialog-content {
    padding: 0 !important;
    border-radius: inherit !important;
    background: inherit !important;
    font-family: inherit !important;
  }

  .rz-dialog-titlebar {
    background: var(--color-secondary) !important;
    border-bottom: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: none !important; /* Hide Radzen's default title bar */

    .rz-dialog-title {
      display: none !important; /* Hide Radzen's default title */
    }
  }

  /* Integration with semantic dialog classes */
  .dialog {
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    background: transparent !important;
    width: 100% !important;
    max-width: 100% !important;
    font-family: inherit !important;
  }

  .dialog-header {
    background: var(--color-secondary) !important;
    border-bottom: none !important;
    padding: var(--spacing-6) !important;
    margin: 0 !important;
  }

  .dialog-content {
    padding: var(--spacing-6) !important;
    background: var(--color-secondary) !important;
    margin: 0 !important;
    color: var(--color-secondary-contrast) !important;
  }

  .dialog-footer {
    background: var(--color-secondary) !important;
    border-top: none !important;
    padding: var(--spacing-4) var(--spacing-6) !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: var(--spacing-3) !important;
  }
}

.rz-dialog-mask {
  background-color: rgb(0 0 0 / 0.5) !important;
  backdrop-filter: blur(2px) !important;
}

/* Dialog size classes when used with DialogService */
.rz-dialog.dialog-sm {
  width: 400px !important;
  max-width: 95vw !important;
}

.rz-dialog.dialog-md {
  width: 500px !important;
  max-width: 95vw !important;
}

.rz-dialog.dialog-lg {
  width: 700px !important;
  max-width: 95vw !important;
}

.rz-dialog.dialog-xl {
  width: 900px !important;
  max-width: 95vw !important;
}

/* Enhanced dialog styling for better visual consistency */
.rz-dialog .dialog-title {
  color: var(--color-gray-900) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-size: var(--font-size-lg) !important;
  margin: 0 !important;
  line-height: 1.5 !important;
}

/* Ensure proper button styling in dialogs */
.rz-dialog .btn {
  font-family: inherit !important;
}

/* Fix any text color issues in dialog content */
.rz-dialog .dialog-content p,
.rz-dialog .dialog-content span,
.rz-dialog .dialog-content div {
  color: inherit !important;
}

/* Ensure form elements in dialogs have proper styling */
.rz-dialog .form-input,
.rz-dialog .form-label,
.rz-dialog .form-error {
  font-family: inherit !important;
}

/* Fix dialog close button styling */
.rz-dialog .rz-dialog-titlebar-close {
  color: var(--color-gray-500) !important;
  background: transparent !important;
  border: none !important;
  padding: var(--spacing-2) !important;
  border-radius: var(--radius-md) !important;
  transition: all var(--transition-normal) !important;
}

.rz-dialog .rz-dialog-titlebar-close:hover {
  color: var(--color-gray-700) !important;
  background: var(--color-gray-100) !important;
}

/* ===== RADZEN CONFIRMATION DIALOG OVERRIDES ===== */
.rz-dialog.barret-confirmation-dialog {
  /* Apply unified styling to confirmation dialogs */
  .rz-dialog-content {
    padding: var(--spacing-6) !important;
    background: var(--color-secondary) !important;
    color: var(--color-secondary-contrast) !important;
  }
}

/* Target Radzen's confirmation dialog buttons specifically */
.rz-dialog-confirm-buttons {
  display: flex !important;
  justify-content: flex-end !important;
  gap: var(--spacing-3) !important;
  padding: var(--spacing-4) var(--spacing-6) !important;
  background: var(--color-secondary) !important;
  border-top: none !important;

  .rz-button {
    /* Apply universal button classes to confirmation dialog buttons */
    &.rz-primary {
      /* Apply all primary button styling from universal class */
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-family: inherit !important;
      font-weight: var(--font-weight-medium) !important;
      border-radius: var(--radius-full) !important;
      transition: all var(--transition-normal) !important;
      cursor: pointer !important;
      text-decoration: none !important;
      user-select: none !important;
      white-space: nowrap !important;
      background-color: var(--color-primary) !important;
      color: var(--color-primary-contrast) !important;
      border: none !important;
      padding: var(--spacing-3) var(--spacing-4) !important;
      font-size: var(--font-size-sm) !important;
      height: 2.5rem !important;
      min-width: 5rem !important;

      &:hover:not(.rz-state-disabled) {
        background-color: var(--color-primary-hover) !important;
        transform: translateY(-1px) !important;
        box-shadow: var(--shadow-md) !important;
      }

      &:active:not(.rz-state-disabled) {
        transform: translateY(0) !important;
        box-shadow: var(--shadow-sm) !important;
      }
    }

    &.rz-secondary {
      /* Apply all secondary button styling from universal class */
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-family: inherit !important;
      font-weight: var(--font-weight-medium) !important;
      border-radius: var(--radius-full) !important;
      transition: all var(--transition-normal) !important;
      cursor: pointer !important;
      text-decoration: none !important;
      user-select: none !important;
      white-space: nowrap !important;
      background-color: var(--color-secondary) !important;
      color: var(--color-secondary-contrast) !important;
      border: 1px solid var(--color-gray-300) !important;
      padding: var(--spacing-3) var(--spacing-4) !important;
      font-size: var(--font-size-sm) !important;
      height: 2.5rem !important;
      min-width: 5rem !important;

      &:hover:not(.rz-state-disabled) {
        background-color: var(--color-secondary-hover) !important;
        border-color: var(--color-gray-400) !important;
        transform: translateY(-1px) !important;
        box-shadow: var(--shadow-sm) !important;
      }

      &:active:not(.rz-state-disabled) {
        transform: translateY(0) !important;
        box-shadow: var(--shadow-xs) !important;
      }
    }
  }
}

/* Global styling for ALL Radzen confirmation dialogs */
.rz-dialog-confirm {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--color-gray-200) !important;
  background: var(--color-secondary) !important;

  .rz-dialog-content {
    padding: var(--spacing-6) !important;
    background: var(--color-secondary) !important;
    color: var(--color-secondary-contrast) !important;
    font-family: inherit !important;
  }
}

/* ===== RADZEN FORM CONTROLS ===== */
.rz-textbox, .rz-textarea, .rz-numeric input, .rz-dropdown {
  border: 1px solid var(--color-gray-300) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-3) var(--spacing-4) !important;
  font-size: var(--font-size-sm) !important;
  background-color: var(--color-secondary) !important;
  color: var(--color-secondary-contrast) !important;
  transition: all var(--transition-normal) !important;
  font-family: inherit !important;

  &:focus {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgb(17 24 39 / 0.1) !important;
    outline: none !important;
  }

  &:hover:not(:focus) {
    border-color: var(--color-gray-400) !important;
  }

  &::placeholder {
    color: var(--color-gray-500) !important;
  }
}

/* Radzen Label styling */
.rz-label {
  display: block !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-gray-700) !important;
  margin-bottom: var(--spacing-2) !important;
  font-family: inherit !important;
}

/* ===== RADZEN DATA GRID ===== */
.rz-datatable {
  border: 1px solid var(--color-gray-200) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
  background-color: var(--color-secondary) !important;
  box-shadow: var(--shadow-sm) !important;

  .rz-datatable-header {
    background-color: var(--color-gray-50) !important;
    border-bottom: 1px solid var(--color-gray-200) !important;

    th {
      padding: var(--spacing-4) !important;
      font-weight: var(--font-weight-medium) !important;
      color: var(--color-gray-700) !important;
      font-size: var(--font-size-sm) !important;
      border-right: 1px solid var(--color-gray-200) !important;

      &:last-child {
        border-right: none !important;
      }
    }
  }

  .rz-datatable-data {
    td {
      padding: var(--spacing-4) !important;
      border-bottom: 1px solid var(--color-gray-100) !important;
      border-right: 1px solid var(--color-gray-100) !important;
      font-size: var(--font-size-sm) !important;
      color: var(--color-gray-900) !important;

      &:last-child {
        border-right: none !important;
      }
    }

    tr:hover {
      background-color: var(--color-gray-50) !important;
    }

    tr:last-child td {
      border-bottom: none !important;
    }
  }
}

/* ===== RADZEN CARD ===== */
.rz-card {
  background-color: var(--color-secondary) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid var(--color-gray-200) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all var(--transition-normal) !important;
  overflow: hidden !important;

  &:hover {
    box-shadow: var(--shadow-md) !important;
    border-color: var(--color-gray-300) !important;
  }

  .rz-card-header {
    padding: var(--spacing-6) var(--spacing-6) var(--spacing-4) !important;
    border-bottom: 1px solid var(--color-gray-200) !important;
    background-color: transparent !important;
  }

  .rz-card-content {
    padding: var(--spacing-6) !important;
  }

  .rz-card-footer {
    padding: var(--spacing-4) var(--spacing-6) var(--spacing-6) !important;
    border-top: 1px solid var(--color-gray-200) !important;
    background-color: var(--color-gray-50) !important;
  }
}
