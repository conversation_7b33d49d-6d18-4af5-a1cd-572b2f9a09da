/* Dark Theme - Integrated with <PERSON><PERSON>zen Material Theme */

/* 1. Load design tokens first (provides CSS custom properties) */
@import '../design-system/tokens.css';

/* 2. <PERSON><PERSON>zen Material dark theme (provides base component styling) */
@import './radzen-barret-dark.css';

/* 3. Load our semantic component overrides */
@import '../design-system/components.css';

/* 4. <PERSON><PERSON>-specific overrides (highest specificity) */
@import '../design-system/radzen-overrides.css';

/* Force dark theme overrides */
:root {
  --color-primary: #ffffff;
  --color-primary-hover: #f3f4f6;
  --color-primary-contrast: #000000;

  --color-secondary: #1f2937;
  --color-secondary-hover: #374151;
  --color-secondary-contrast: #ffffff;

  --color-gray-50: #111827;
  --color-gray-100: #1f2937;
  --color-gray-200: #374151;
  --color-gray-300: #4b5563;
  --color-gray-400: #6b7280;
  --color-gray-500: #9ca3af;
  --color-gray-600: #d1d5db;
  --color-gray-700: #e5e7eb;
  --color-gray-800: #f3f4f6;
  --color-gray-900: #ffffff;
}
