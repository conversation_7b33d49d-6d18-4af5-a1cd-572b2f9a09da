/* Light Theme - Integrated with <PERSON>dzen Material Theme */

/* 1. Load design tokens first */
@import '../design-system/tokens.css';

/* 2. Load Radzen Material theme (provides base component styling) */
@import './radzen-barret.css';

/* 3. Load our component overrides */
@import '../design-system/components.css';

/* 4. <PERSON><PERSON>-specific overrides (highest specificity) */
@import '../design-system/radzen-overrides.css';

/* Light theme tokens are the default in tokens.css */
